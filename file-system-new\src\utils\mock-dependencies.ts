/**
 * Mock dependencies for the professional file system tools
 */

// Mock schema validator
export class SchemaValidator {
    static validate(schema: any, params: any): string | null {
        // Basic validation - just check required fields exist
        if (schema.required) {
            for (const field of schema.required) {
                if (params[field] === undefined || params[field] === null) {
                    return `Missing required field: ${field}`;
                }
            }
        }
        return null;
    }
}

// Mock path utilities
export function makeRelative(absolutePath: string, basePath: string): string {
    const path = require('path');
    return path.relative(basePath, absolutePath);
}

export function shortenPath(filePath: string): string {
    if (filePath.length <= 50) return filePath;
    return '...' + filePath.slice(-47);
}

// Mock error utilities
export function getErrorMessage(error: unknown): string {
    if (error instanceof Error) return error.message;
    return String(error);
}

export function isNodeError(error: unknown): error is NodeJS.ErrnoException {
    return error instanceof Error && 'code' in error;
}

// Mock file utilities
export function isWithinRoot(filePath: string, rootPath: string): boolean {
    const path = require('path');
    const absolutePath = path.resolve(filePath);
    const absoluteRoot = path.resolve(rootPath);
    return absolutePath.startsWith(absoluteRoot);
}

export function getSpecificMimeType(filePath: string): string {
    const path = require('path');
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
        '.txt': 'text/plain',
        '.js': 'text/javascript',
        '.ts': 'text/typescript',
        '.json': 'application/json',
        '.md': 'text/markdown',
        '.html': 'text/html',
        '.css': 'text/css',
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.pdf': 'application/pdf'
    };
    return mimeTypes[ext] || 'application/octet-stream';
}

export async function processSingleFileContent(
    filePath: string,
    rootDir: string,
    offset?: number,
    limit?: number
): Promise<{ llmContent: string | any, returnDisplay?: string, error?: string }> {
    const fs = require('fs');
    const path = require('path');
    
    try {
        if (!fs.existsSync(filePath)) {
            return { 
                llmContent: '', 
                error: `File not found: ${filePath}`,
                returnDisplay: `File not found: ${path.relative(rootDir, filePath)}`
            };
        }
        
        const stats = fs.statSync(filePath);
        if (!stats.isFile()) {
            return { 
                llmContent: '', 
                error: `Path is not a file: ${filePath}`,
                returnDisplay: `Path is not a file: ${path.relative(rootDir, filePath)}`
            };
        }
        
        // Check if it's a media file
        const ext = path.extname(filePath).toLowerCase();
        if (['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.pdf'].includes(ext)) {
            const buffer = fs.readFileSync(filePath);
            const base64Data = buffer.toString('base64');
            const mimeType = getSpecificMimeType(filePath);
            
            return {
                llmContent: {
                    inlineData: {
                        mimeType: mimeType,
                        data: base64Data
                    }
                },
                returnDisplay: `Media file: ${path.relative(rootDir, filePath)}`
            };
        }
        
        // Check if it's binary
        const buffer = fs.readFileSync(filePath);
        const chunk = buffer.slice(0, 512);
        let isBinary = false;
        for (let i = 0; i < chunk.length; i++) {
            if (chunk[i] === 0) {
                isBinary = true;
                break;
            }
        }
        
        if (isBinary) {
            return { 
                llmContent: `Cannot display content of binary file: ${filePath}`,
                returnDisplay: `Binary file: ${path.relative(rootDir, filePath)}`
            };
        }
        
        // Read text file
        const content = fs.readFileSync(filePath, 'utf-8');
        const lines = content.split('\n');
        
        // Handle offset and limit
        if (offset !== undefined || limit !== undefined) {
            const startLine = offset || 0;
            const maxLines = limit || 2000;
            
            if (startLine >= lines.length) {
                return {
                    llmContent: `Error: Offset ${startLine} is beyond file length (${lines.length} lines)`,
                    returnDisplay: `Offset error for ${path.relative(rootDir, filePath)}`
                };
            }
            
            const endLine = Math.min(startLine + maxLines, lines.length);
            const selectedLines = lines.slice(startLine, endLine);
            
            let result = selectedLines.join('\n');
            
            if (startLine > 0 || endLine < lines.length) {
                const prefix = `[File content truncated: showing lines ${startLine + 1}-${endLine} of ${lines.length} total lines...]\n`;
                result = prefix + result;
            }
            
            return {
                llmContent: result,
                returnDisplay: `${path.relative(rootDir, filePath)} (lines ${startLine + 1}-${endLine})`
            };
        }
        
        // Return full content, but truncate if too long
        const maxLines = 2000;
        if (lines.length > maxLines) {
            const truncatedLines = lines.slice(0, maxLines);
            const prefix = `[File content truncated: showing first ${maxLines} of ${lines.length} total lines...]\n`;
            const result = prefix + truncatedLines.join('\n');
            
            return {
                llmContent: result,
                returnDisplay: `${path.relative(rootDir, filePath)} (truncated)`
            };
        }
        
        return {
            llmContent: content,
            returnDisplay: path.relative(rootDir, filePath)
        };
        
    } catch (error) {
        return { 
            llmContent: '', 
            error: `Error reading file: ${getErrorMessage(error)}`,
            returnDisplay: `Error reading ${path.relative(rootDir, filePath)}`
        };
    }
}

// Mock edit corrector - simplified version
export async function ensureCorrectEdit(
    filePath: string,
    currentContent: string,
    params: any,
    geminiClient: any,
    abortSignal: AbortSignal
): Promise<{ params: any, occurrences: number }> {
    // Simple implementation - just count occurrences
    const occurrences = (currentContent.match(new RegExp(escapeRegExp(params.old_string), 'g')) || []).length;
    return { params, occurrences };
}

export async function ensureCorrectFileContent(
    content: string,
    geminiClient: any,
    abortSignal: AbortSignal
): Promise<string> {
    // Simple implementation - just return the content as-is
    return content;
}

function escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Mock telemetry
export function recordFileOperationMetric(
    config: any,
    operation: string,
    lines?: number,
    mimeType?: string,
    extension?: string
): void {
    // No-op for our use case
}

export enum FileOperation {
    READ = 'read',
    WRITE = 'write',
    CREATE = 'create',
    UPDATE = 'update'
}

// Mock diff options
export const DEFAULT_DIFF_OPTIONS = {
    context: 3
};

// Mock tool types
export enum Icon {
    FileSearch = 'file-search',
    Pencil = 'pencil'
}

export enum Type {
    STRING = 'string',
    NUMBER = 'number',
    OBJECT = 'object'
}

export class BaseTool<TParams, TResult> {
    constructor(
        public name: string,
        public displayName: string,
        public description: string,
        public icon: Icon,
        public schema: any
    ) {}
    
    validateToolParams(params: TParams): string | null {
        return null;
    }
    
    getDescription(params: TParams): string {
        return this.description;
    }
    
    toolLocations(params: TParams): any[] {
        return [];
    }
    
    async execute(params: TParams, signal: AbortSignal): Promise<TResult> {
        throw new Error('Not implemented');
    }
}

export interface ToolResult {
    llmContent: string;
    returnDisplay?: any;
}

export interface ModifiableTool<TParams> {
    getModifyContext(signal: AbortSignal): any;
}

export interface ModifyContext<TParams> {
    getFilePath: (params: TParams) => string;
    getCurrentContent: (params: TParams) => Promise<string>;
    getProposedContent: (params: TParams) => Promise<string>;
    createUpdatedParams: (oldContent: string, newContent: string, params: TParams) => TParams;
}
