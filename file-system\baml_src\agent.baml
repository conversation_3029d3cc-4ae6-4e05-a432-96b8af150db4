// human tools are async requests to a human
type HumanTools = ClarificationRequest | DoneForNow

class ClarificationRequest {
  intent "request_more_information" @description("you can request more information from me")
  message string
}

class DoneForNow {
  intent "done_for_now"

  message string @description(#"
    message to send to the user about the work that was done. 
  "#)
}

client<llm> Qwen3 {
  provider openai
  options {
    model "qwen2.5-32b-instruct-int4"
    api_key "974fd8d1c155aa3d04b17bf253176b5e"
    base_url "https://gateway.chat.sensedeal.vip/v1"
  }
}

function DetermineNextStep(
    thread: string
) -> HumanTools | FileSystemRead | FileSystemWrite | FileSystemReplace {
    client Qwen3

    // client "openai/gpt-4o"

    // use /nothink for now because the thinking tokens (or streaming thereof) screw with baml (i think (no pun intended))
    prompt #"
        {{ _.role("system") }}

        /nothink

        You are a helpful assistant that can help with tasks.

        IMPORTANT FILE PATH RULES:
        - Always use relative paths (e.g., "temp.md", "src/file.ts", "docs/readme.md")
        - NEVER use absolute paths starting with "/" or "C:\"
        - All file operations are relative to the current working directory
        - If a user mentions a file name without path, assume it's in the current directory
        - Pay attention to conversation context - if recent files are mentioned, use those paths
        - When user says "that file", "the file", or similar, refer to the most recently accessed file
        - For partial file names, try to match against recently accessed files

        {{ _.role("user") }}

        You are working on the following thread:

        {{ thread }}

        What should the next step be?

        {{ ctx.output_format }}
    "#
}

test HelloWorld {
  functions [DetermineNextStep]
  args {
    thread #"
      <user_input>
        hello!
      </user_input>
    "#
  }
  @@assert(intent, {{this.intent == "request_more_information"}})
}



test FileRead {
  functions [DetermineNextStep]
  args {
    thread #"
      <user_input>
      read the file test.txt
      </user_input>
    "#
  }
  @@assert(intent, {{this.intent == "read_file"}})
  @@assert(path, {{this.path == "test.txt"}})
}

test FileWrite {
  functions [DetermineNextStep]
  args {
    thread #"
      <user_input>
      create a file called hello.txt with content 'Hello World!'
      </user_input>
    "#
  }
  @@assert(intent, {{this.intent == "write_file"}})
  @@assert(file_path, {{"hello.txt" in this.file_path}})
}

test FileReplace {
  functions [DetermineNextStep]
  args {
    thread #"
      <user_input>
      I need to replace the text 'old content' with 'new content' in the file data.txt
      </user_input>
    "#
  }
  @@assert(intent, {{this.intent == "replace"}})
  @@assert(file_path, {{"data.txt" in this.file_path}})
}
        