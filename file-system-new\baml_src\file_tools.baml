// File system tools for reading, writing, and editing files
type FileSystemTools = ReadFileTool | WriteFileTool | ReplaceTool

class ReadFileTool {
    intent "read_file"
    path string @description("The absolute path to the file to read")
    offset int? @description("For text files, the 0-based line number to start reading from")
    limit int? @description("For text files, the maximum number of lines to read")
}

class WriteFileTool {
    intent "write_file"
    file_path string @description("The absolute path to the file to write to")
    content string @description("The content to write into the file")
}

class ReplaceTool {
    intent "replace"
    file_path string @description("The absolute path to the file to modify")
    old_string string @description("The exact literal text to replace. Must include sufficient context (3+ lines before/after)")
    new_string string @description("The exact literal text to replace old_string with")
    expected_replacements int? @description("The number of occurrences to replace. Defaults to 1")
}
