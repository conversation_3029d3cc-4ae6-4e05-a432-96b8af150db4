class DoneForNow {
  intent "done_for_now"
  message string 
}

client<llm> Qwen3 {
  provider "openai-generic"
  options {
    base_url env.BASETEN_BASE_URL
    api_key env.BASETEN_API_KEY 
  }
}
function DetermineNextStep(
    thread: string 
) -> CalculatorTools | DoneForNow {
    client Qwen3

    // client "openai/gpt-4o"

    prompt #"
        {{ _.role("system") }}

        /nothink

        You are a helpful assistant that can help with tasks.

        {{ _.role("user") }}

        You are working on the following thread:

        {{ thread }}

        What should the next step be?

        {{ ctx.output_format }}
    "#
}

test HelloWorld {
  functions [DetermineNextStep]
  args {
    thread #"
      {
        "type": "user_input",
        "data": "hello!"
      }
    "#
  }
  @@assert(intent, {{this.intent == "done_for_now"}})
}

test MathOperation {
  functions [DetermineNextStep]
  args {
    thread #"
      {
        "type": "user_input",
        "data": "can you multiply 3 and 4?"
      }
    "#
  }
  @@assert(intent, {{this.intent == "multiply"}})
}

test LongMath {
  functions [DetermineNextStep]
  args {
    thread #"
      [
        {
          "type": "user_input",
          "data": "can you multiply 3 and 4, then divide the result by 2 and then add 12 to that result?"
        },
        {
          "type": "tool_call",
          "data": {
            "intent": "multiply",
            "a": 3,
            "b": 4
          }
        },
        {
          "type": "tool_response",
          "data": 12
        },
        {
          "type": "tool_call", 
          "data": {
            "intent": "divide",
            "a": 12,
            "b": 2
          }
        },
        {
          "type": "tool_response",
          "data": 6
        },
        {
          "type": "tool_call",
          "data": {
            "intent": "add", 
            "a": 6,
            "b": 12
          }
        },
        {
          "type": "tool_response",
          "data": 18
        }
      ]
    "#
  }
  @@assert(intent, {{this.intent == "done_for_now"}})
  @@assert(answer, {{"18" in this.message}})
}

