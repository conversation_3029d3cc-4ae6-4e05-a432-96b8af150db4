import { ReadFileTool, WriteFileTool, ReplaceTool, b } from "../baml_client";
import { fileRead } from "./file-read";
import { fileWrite } from "./file-write";
import { fileReplace } from "./file-replace";
import { sessionManager } from "./session-manager";
import { FileOperation } from "./state";

export interface Event {
    type: string
    data: any;
}

export class Thread {
    events: Event[] = [];

    constructor(events: Event[]) {
        this.events = events;
    }

    serializeForLLM() {
        return this.events.map(e => this.serializeOneEvent(e)).join("\n");
    }

    trimLeadingWhitespace(s: string) {
        return s.replace(/^[ \t]+/gm, '');
    }

    serializeOneEvent(e: Event) {
        return this.trimLeadingWhitespace(`
            <${e.data?.intent || e.type}>
            ${
            typeof e.data !== 'object' ? e.data :
            Object.keys(e.data).filter(k => k !== 'intent').map(k => `${k}: ${e.data[k]}`).join("\n")}
            </${e.data?.intent || e.type}>
        `)
    }

    awaitingHumanResponse(): boolean {
        const lastEvent = this.events[this.events.length - 1];
        return ['request_more_information', 'done_for_now'].includes(lastEvent.data.intent);
    }

    awaitingHumanApproval(): boolean {
        const lastEvent = this.events[this.events.length - 1];
        return false; // No human approval needed for file operations
    }
}

export type FileSystemTool = ReadFileTool | WriteFileTool | ReplaceTool;

export async function handleFileSystemTool(tool: FileSystemTool, thread: Thread): Promise<Thread> {
    let result: string;
    let resolvedTool = { ...tool };

    // Resolve file paths using session context
    if (tool.intent === 'read_file') {
        const readTool = tool as ReadFileTool;
        const resolvedPath = sessionManager.resolveFilePath(readTool.path);
        (resolvedTool as ReadFileTool).path = resolvedPath;
        console.log(`Resolved file path: ${readTool.path} -> ${resolvedPath}`);
    } else if (tool.intent === 'write_file') {
        const writeTool = tool as WriteFileTool;
        const resolvedPath = sessionManager.resolveFilePath(writeTool.file_path);
        (resolvedTool as WriteFileTool).file_path = resolvedPath;
        console.log(`Resolved file path: ${writeTool.file_path} -> ${resolvedPath}`);
    } else if (tool.intent === 'replace') {
        const replaceTool = tool as ReplaceTool;
        const resolvedPath = sessionManager.resolveFilePath(replaceTool.file_path);
        (resolvedTool as ReplaceTool).file_path = resolvedPath;
        console.log(`Resolved file path: ${replaceTool.file_path} -> ${resolvedPath}`);
    }

    switch (tool.intent) {
        case "read_file":
            result = await fileRead(resolvedTool as ReadFileTool);
            console.log("file_tool_response", result);

            // Record the operation
            const readOperation: FileOperation = {
                type: 'read',
                filePath: (resolvedTool as ReadFileTool).path,
                timestamp: new Date(),
                success: !result.startsWith('Error'),
                details: result
            };
            sessionManager.addFileOperation(readOperation);

            thread.events.push({
                "type": "tool_response",
                "data": result
            });
            return thread;

        case "write_file":
            result = await fileWrite(resolvedTool as WriteFileTool);
            console.log("file_tool_response", result);

            // Record the operation
            const writeOperation: FileOperation = {
                type: 'write',
                filePath: (resolvedTool as WriteFileTool).file_path,
                timestamp: new Date(),
                success: !result.startsWith('Error'),
                details: result
            };
            sessionManager.addFileOperation(writeOperation);

            thread.events.push({
                "type": "tool_response",
                "data": result
            });
            return thread;

        case "replace":
            result = await fileReplace(resolvedTool as ReplaceTool);
            console.log("file_tool_response", result);

            // Record the operation
            const replaceOperation: FileOperation = {
                type: 'replace',
                filePath: (resolvedTool as ReplaceTool).file_path,
                timestamp: new Date(),
                success: result.startsWith('Successfully'),
                details: result
            };
            sessionManager.addFileOperation(replaceOperation);

            thread.events.push({
                "type": "tool_response",
                "data": result
            });
            return thread;
    }
}



export async function agentLoop(thread: Thread): Promise<Thread> {
    // Update the session with current thread
    sessionManager.updateThread(thread);

    while (true) {
        // Add conversation context to the thread serialization
        const conversationSummary = sessionManager.getConversationSummary();
        let threadContext = thread.serializeForLLM();

        if (conversationSummary) {
            threadContext = `<conversation_context>\n${conversationSummary}</conversation_context>\n\n${threadContext}`;
        }

        const nextStep = await b.DetermineNextStep(threadContext);
        console.log("nextStep", nextStep);

        thread.events.push({
            "type": "tool_call",
            "data": nextStep
        });

        switch (nextStep.intent) {
            case "done_for_now":
            case "request_more_information":
                // Update session before returning
                sessionManager.updateThread(thread);
                return thread;
            case "read_file":
            case "write_file":
            case "replace":
                thread = await handleFileSystemTool(nextStep, thread);
                // Update session after each tool execution
                sessionManager.updateThread(thread);
                break;
        }
    }
}


