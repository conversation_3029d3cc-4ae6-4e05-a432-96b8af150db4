import { ReadFileTool, WriteFileTool, ReplaceTool, b } from "../baml_client";
import { fileRead } from "./file-read";
import { fileWrite } from "./file-write";
import { fileReplace } from "./file-replace";

export interface Event {
    type: string
    data: any;
}

export class Thread {
    events: Event[] = [];

    constructor(events: Event[]) {
        this.events = events;
    }

    serializeForLLM() {
        return this.events.map(e => this.serializeOneEvent(e)).join("\n");
    }

    trimLeadingWhitespace(s: string) {
        return s.replace(/^[ \t]+/gm, '');
    }

    serializeOneEvent(e: Event) {
        return this.trimLeadingWhitespace(`
            <${e.data?.intent || e.type}>
            ${
            typeof e.data !== 'object' ? e.data :
            Object.keys(e.data).filter(k => k !== 'intent').map(k => `${k}: ${e.data[k]}`).join("\n")}
            </${e.data?.intent || e.type}>
        `)
    }

    awaitingHumanResponse(): boolean {
        const lastEvent = this.events[this.events.length - 1];
        return ['request_more_information', 'done_for_now'].includes(lastEvent.data.intent);
    }

    awaitingHumanApproval(): boolean {
        const lastEvent = this.events[this.events.length - 1];
        return false; // No human approval needed for file operations
    }
}

export type FileSystemTool = ReadFileTool | WriteFileTool | ReplaceTool;

export async function handleFileSystemTool(tool: FileSystemTool, thread: Thread): Promise<Thread> {
    let result: string;

    switch (tool.intent) {
        case "read_file":
            result = await fileRead(tool);
            console.log("file_tool_response", result);
            thread.events.push({
                "type": "tool_response",
                "data": result
            });
            return thread;

        case "write_file":
            result = await fileWrite(tool);
            console.log("file_tool_response", result);
            thread.events.push({
                "type": "tool_response",
                "data": result
            });
            return thread;

        case "replace":
            result = await fileReplace(tool);
            console.log("file_tool_response", result);
            thread.events.push({
                "type": "tool_response",
                "data": result
            });
            return thread;
    }
}



export async function agentLoop(thread: Thread): Promise<Thread> {

    while (true) {
        const nextStep = await b.DetermineNextStep(thread.serializeForLLM());
        console.log("nextStep", nextStep);

        thread.events.push({
            "type": "tool_call",
            "data": nextStep
        });

        switch (nextStep.intent) {
            case "done_for_now":
            case "request_more_information":
                // response to human, return the thread
                return thread;
            case "read_file":
            case "write_file":
            case "replace":
                thread = await handleFileSystemTool(nextStep, thread);
                break;
        }
    }
}


