// File system tools for reading, writing, and editing files
type FileSystemRead = ReadFileTool

class ReadFileTool {
    intent "read_file"
    path string @description("The relative path to the file to read (e.g., 'temp.md', 'src/file.ts'). Do NOT use absolute paths starting with '/' or 'C:'")
    offset int? @description("For text files, the 0-based line number to start reading from")
    limit int? @description("For text files, the maximum number of lines to read")
}