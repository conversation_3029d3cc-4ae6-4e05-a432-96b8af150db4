#hello

本项目基于BAML框架实现了三个强大的文件系统工具：`read_file`、`write_file`和`replace`。这些工具遵循file-system.md规范，并与现有的计算器工具和人机交互功能完全集成。

## 实现的工具

### 1. `read_file` (ReadFile)
读取指定文件的内容，支持文本文件、图像文件和PDF文件。

**功能特性：**
- 支持文本文件的分页读取（offset和limit参数）
- 自动检测并处理二进制文件
- 图像和PDF文件返回base64编码格式
- 安全的路径验证，限制在根目录内

**使用示例：**
```
```bash
npx tsx src/index.ts "read the file test.txt"
npx tsx src/index.ts "read lines 10-20 from config.json"
```

### 2. `write_file` (WriteFile)
创建新文件或覆盖现有文件的内容。

**功能特性：**
- 自动创建父目录（如果不存在）
- 支持相对路径和绝对路径
- 安全的路径验证
- 明确的成功/失败反馈

**使用示例：**

```bash
npx tsx src/index.ts "create a file called hello.txt with content 'Hello World!'\"
npx tsx src/index.ts "write the calculation result to output.txt"
```

### 3. `replace` (Edit)
在文件中替换指定的文本内容。

**功能特性：**
- 精确的文本匹配和替换
- 支持指定替换次数
- 如果old_string为空，则创建新文件
- 详细的错误报告（未找到、多次匹配等）

**使用示例：**

```bash
npx tsx src/index.ts "replace 'Hello World!' with 'Hello BAML!' in hello.txt"
npx tsx src/index.ts "update the version number in package.json"
```
