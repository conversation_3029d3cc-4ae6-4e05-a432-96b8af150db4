import * as fs from 'fs';
import * as path from 'path';
import { ReadFileTool } from '../baml_client/types';

export type FileSystemRead = ReadFileTool;

const ROOT_DIRECTORY = process.cwd();


/**
 * 验证路径是否在根目录内
 */
function validatePath(filePath: string): string {
    // Normalize the path and remove any leading slashes for relative paths
    let normalizedPath = filePath;

    // If the path starts with '/' but is meant to be relative, remove the leading slash
    if (normalizedPath.startsWith('/') && !path.isAbsolute(normalizedPath)) {
        normalizedPath = normalizedPath.substring(1);
    }

    // If path is not absolute, resolve it relative to ROOT_DIRECTORY
    const absolutePath = path.isAbsolute(normalizedPath) ?
        path.resolve(normalizedPath) :
        path.resolve(ROOT_DIRECTORY, normalizedPath);

    const rootPath = path.resolve(ROOT_DIRECTORY);

    if (!absolutePath.startsWith(rootPath)) {
        throw new Error(`Path ${filePath} is outside the allowed root directory. Please use relative paths like 'temp.md' or 'src/file.ts'`);
    }

    return absolutePath;
}

/**
 * 通过检查文件的内容来检查文件是否是二进制文件
 */
function isBinaryFile(filePath: string): boolean {
    try {
        const buffer = fs.readFileSync(filePath);
        const chunk = buffer.slice(0, 512);
        
        // Check for null bytes which indicate binary content
        for (let i = 0; i < chunk.length; i++) {
            if (chunk[i] === 0) {
                return true;
            }
        }
        
        return false;
    } catch (error) {
        return false;
    }
}

/**
 * Gets the MIME type based on file extension
 */
function getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml',
        '.bmp': 'image/bmp',
        '.pdf': 'application/pdf'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * Checks if a file is an image or PDF that should be returned as base64
 */
function isMediaFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.pdf'].includes(ext);
}

/**
 * Reads a file and returns its content
 */
export async function fileRead(tool: ReadFileTool): Promise<string> {
    try {
        const absolutePath = validatePath(tool.path);
        
        if (!fs.existsSync(absolutePath)) {
            return `Error: File not found: ${tool.path}`;
        }
        
        if (!fs.statSync(absolutePath).isFile()) {
            return `Error: Path is not a file: ${tool.path}`;
        }
        
        // Handle media files (images, PDFs)
        if (isMediaFile(absolutePath)) {
            const buffer = fs.readFileSync(absolutePath);
            const base64Data = buffer.toString('base64');
            const mimeType = getMimeType(absolutePath);
            
            return JSON.stringify({
                inlineData: {
                    mimeType: mimeType,
                    data: base64Data
                }
            });
        }
        
        // Check if it's a binary file
        if (isBinaryFile(absolutePath)) {
            return `Cannot display content of binary file: ${tool.path}`;
        }
        
        // Read text file
        const content = fs.readFileSync(absolutePath, 'utf-8');
        const lines = content.split('\n');
        
        // Handle offset and limit for text files
        if (tool.offset !== undefined || tool.limit !== undefined) {
            const offset = tool.offset || 0;
            const limit = tool.limit || 2000;
            
            if (offset >= lines.length) {
                return `Error: Offset ${offset} is beyond file length (${lines.length} lines)`;
            }
            
            const endLine = Math.min(offset + limit, lines.length);
            const selectedLines = lines.slice(offset, endLine);
            
            let result = selectedLines.join('\n');
            
            if (offset > 0 || endLine < lines.length) {
                const prefix = `[File content truncated: showing lines ${offset + 1}-${endLine} of ${lines.length} total lines...]\n`;
                result = prefix + result;
            }
            
            return result;
        }
        
        // Return full content, but truncate if too long
        const maxLines = 2000;
        if (lines.length > maxLines) {
            const truncatedLines = lines.slice(0, maxLines);
            const prefix = `[File content truncated: showing first ${maxLines} of ${lines.length} total lines...]\n`;
            return prefix + truncatedLines.join('\n');
        }
        
        return content;
        
    } catch (error) {
        return `Error reading file: ${error instanceof Error ? error.message : String(error)}`;
    }
}
