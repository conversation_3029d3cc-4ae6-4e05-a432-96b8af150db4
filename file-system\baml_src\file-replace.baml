// File system tools for reading, writing, and editing files
type FileSystemReplace = ReplaceTool

class ReplaceTool {
    intent "replace"
    file_path string @description("The relative path to the file to modify (e.g., 'temp.md', 'src/file.ts'). Do NOT use absolute paths starting with '/' or 'C:'")
    old_string string @description("The exact literal text to replace. Must include sufficient context (3+ lines before/after)")
    new_string string @description("The exact literal text to replace old_string with")
    expected_replacements int? @description("The number of occurrences to replace. Defaults to 1")
}