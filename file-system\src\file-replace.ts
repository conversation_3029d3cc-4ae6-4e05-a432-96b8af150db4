import * as fs from 'fs';
import * as path from 'path';
import { ReplaceTool } from '../baml_client/types';

export type FileSystemReplace = ReplaceTool;

const ROOT_DIRECTORY = process.cwd();


/**
 * 验证路径是否在根目录内
 */
function validatePath(filePath: string): string {
    // Normalize the path and remove any leading slashes for relative paths
    let normalizedPath = filePath;

    // If the path starts with '/' but is meant to be relative, remove the leading slash
    if (normalizedPath.startsWith('/') && !path.isAbsolute(normalizedPath)) {
        normalizedPath = normalizedPath.substring(1);
    }

    // If path is not absolute, resolve it relative to ROOT_DIRECTORY
    const absolutePath = path.isAbsolute(normalizedPath) ?
        path.resolve(normalizedPath) :
        path.resolve(ROOT_DIRECTORY, normalizedPath);

    const rootPath = path.resolve(ROOT_DIRECTORY);

    if (!absolutePath.startsWith(rootPath)) {
        throw new Error(`Path ${filePath} is outside the allowed root directory. Please use relative paths like 'temp.md' or 'src/file.ts'`);
    }

    return absolutePath;
}

/**
 * 通过检查文件的内容来检查文件是否是二进制文件
 */
function isBinaryFile(filePath: string): boolean {
    try {
        const buffer = fs.readFileSync(filePath);
        const chunk = buffer.slice(0, 512);
        
        // Check for null bytes which indicate binary content
        for (let i = 0; i < chunk.length; i++) {
            if (chunk[i] === 0) {
                return true;
            }
        }
        
        return false;
    } catch (error) {
        return false;
    }
}

/**
 * Gets the MIME type based on file extension
 */
function getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml',
        '.bmp': 'image/bmp',
        '.pdf': 'application/pdf'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * Checks if a file is an image or PDF that should be returned as base64
 */
function isMediaFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.pdf'].includes(ext);
}

/**
 * Escapes special regex characters in a string
 */
function escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Replaces text within a file
 */
export async function fileReplace(tool: ReplaceTool): Promise<string> {
    try {
        const absolutePath = validatePath(tool.file_path);
        
        // Handle creating new file if old_string is empty
        if (tool.old_string === '') {
            if (fs.existsSync(absolutePath)) {
                return `Error: Cannot create new file, file already exists: ${tool.file_path}`;
            }
            
            const parentDir = path.dirname(absolutePath);
            if (!fs.existsSync(parentDir)) {
                fs.mkdirSync(parentDir, { recursive: true });
            }
            
            fs.writeFileSync(absolutePath, tool.new_string, 'utf-8');
            return `Created new file: ${tool.file_path} with provided content.`;
        }
        
        // Read existing file
        if (!fs.existsSync(absolutePath)) {
            return `Error: File not found: ${tool.file_path}`;
        }
        
        if (!fs.statSync(absolutePath).isFile()) {
            return `Error: Path is not a file: ${tool.file_path}`;
        }
        
        const content = fs.readFileSync(absolutePath, 'utf-8');
        
        // Count occurrences of old_string
        const occurrences = (content.match(new RegExp(escapeRegExp(tool.old_string), 'g')) || []).length;
        const expectedReplacements = tool.expected_replacements || 1;
        
        if (occurrences === 0) {
            return `Failed to edit, 0 occurrences found of the specified text in ${tool.file_path}`;
        }
        
        if (occurrences !== expectedReplacements) {
            return `Failed to edit, expected ${expectedReplacements} occurrences but found ${occurrences} in ${tool.file_path}`;
        }
        
        // Perform replacement
        const newContent = content.replace(new RegExp(escapeRegExp(tool.old_string), 'g'), tool.new_string);
        
        // Write back to file
        fs.writeFileSync(absolutePath, newContent, 'utf-8');
        
        return `Successfully modified file: ${tool.file_path} (${occurrences} replacements).`;
        
    } catch (error) {
        return `Error replacing in file: ${error instanceof Error ? error.message : String(error)}`;
    }
}