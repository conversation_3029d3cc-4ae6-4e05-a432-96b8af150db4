import { ThreadStore, FileOperation, ConversationContext } from './state';
import { Thread } from './agent';

export class SessionManager {
    private static instance: SessionManager;
    private threadStore: ThreadStore;
    private currentSessionId: string | null = null;

    private constructor() {
        this.threadStore = new ThreadStore();
    }

    static getInstance(): SessionManager {
        if (!SessionManager.instance) {
            SessionManager.instance = new SessionManager();
        }
        return SessionManager.instance;
    }

    createSession(initialThread?: Thread): string {
        const thread = initialThread || new Thread([]);
        const sessionId = this.threadStore.create(thread);
        this.currentSessionId = sessionId;
        return sessionId;
    }

    getCurrentSession(): string | null {
        return this.currentSessionId;
    }

    setCurrentSession(sessionId: string): boolean {
        if (this.threadStore.get(sessionId)) {
            this.currentSessionId = sessionId;
            return true;
        }
        return false;
    }

    getThread(sessionId?: string): Thread | undefined {
        const id = sessionId || this.currentSessionId;
        return id ? this.threadStore.get(id) : undefined;
    }

    updateThread(thread: Thread, sessionId?: string): void {
        const id = sessionId || this.currentSessionId;
        if (id) {
            this.threadStore.update(id, thread);
        }
    }

    getContext(sessionId?: string): ConversationContext | undefined {
        const id = sessionId || this.currentSessionId;
        return id ? this.threadStore.getContext(id) : undefined;
    }

    addFileOperation(operation: FileOperation, sessionId?: string): void {
        const id = sessionId || this.currentSessionId;
        if (id) {
            this.threadStore.addFileOperation(id, operation);
        }
    }

    findMostLikelyFile(partialPath: string, sessionId?: string): string | undefined {
        const id = sessionId || this.currentSessionId;
        return id ? this.threadStore.findMostLikelyFile(id, partialPath) : undefined;
    }

    getRecentFiles(sessionId?: string): string[] {
        const context = this.getContext(sessionId);
        return context ? context.recentFiles : [];
    }

    getFileOperationHistory(sessionId?: string): FileOperation[] {
        const context = this.getContext(sessionId);
        return context ? context.fileOperations : [];
    }

    // Helper method to resolve file paths with context
    resolveFilePath(inputPath: string, sessionId?: string): string {
        // If the path looks complete, return as is
        if (inputPath.includes('/') || inputPath.includes('\\') || inputPath.includes('.')) {
            return inputPath;
        }

        // Try to find the most likely file based on context
        const resolvedPath = this.findMostLikelyFile(inputPath, sessionId);
        return resolvedPath || inputPath;
    }

    // Get conversation summary for context
    getConversationSummary(sessionId?: string): string {
        const context = this.getContext(sessionId);
        if (!context) return '';

        let summary = '';
        
        if (context.recentFiles.length > 0) {
            summary += `Recent files: ${context.recentFiles.slice(0, 5).join(', ')}\n`;
        }
        
        if (context.lastActiveFile) {
            summary += `Last active file: ${context.lastActiveFile}\n`;
        }
        
        const recentOps = context.fileOperations.slice(-3);
        if (recentOps.length > 0) {
            summary += `Recent operations:\n`;
            recentOps.forEach(op => {
                summary += `  - ${op.type} on ${op.filePath} (${op.success ? 'success' : 'failed'})\n`;
            });
        }
        
        return summary;
    }
}

// Export a singleton instance
export const sessionManager = SessionManager.getInstance();
