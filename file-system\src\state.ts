import crypto from 'crypto';
import { Thread } from '../src/agent';

export interface FileOperation {
    type: 'read' | 'write' | 'replace';
    filePath: string;
    timestamp: Date;
    success: boolean;
    details?: string;
}

export interface ConversationContext {
    recentFiles: string[];
    fileOperations: FileOperation[];
    lastActiveFile?: string;
}

// you can replace this with any simple state management,
// e.g. redis, sqlite, postgres, etc
export class ThreadStore {
    private threads: Map<string, Thread> = new Map();
    private contexts: Map<string, ConversationContext> = new Map();

    create(thread: Thread): string {
        const id = crypto.randomUUID();
        this.threads.set(id, thread);
        this.contexts.set(id, {
            recentFiles: [],
            fileOperations: [],
            lastActiveFile: undefined
        });
        return id;
    }

    get(id: string): Thread | undefined {
        return this.threads.get(id);
    }

    update(id: string, thread: Thread): void {
        this.threads.set(id, thread);
    }

    getContext(id: string): ConversationContext | undefined {
        return this.contexts.get(id);
    }

    updateContext(id: string, context: ConversationContext): void {
        this.contexts.set(id, context);
    }

    addFileOperation(id: string, operation: FileOperation): void {
        const context = this.contexts.get(id);
        if (context) {
            context.fileOperations.push(operation);

            // Update recent files list
            if (!context.recentFiles.includes(operation.filePath)) {
                context.recentFiles.unshift(operation.filePath);
                // Keep only last 10 files
                context.recentFiles = context.recentFiles.slice(0, 10);
            }

            // Update last active file if operation was successful
            if (operation.success) {
                context.lastActiveFile = operation.filePath;
            }

            this.contexts.set(id, context);
        }
    }

    findMostLikelyFile(id: string, partialPath: string): string | undefined {
        const context = this.contexts.get(id);
        if (!context) return undefined;

        // If it's already a complete path, return as is
        if (partialPath.includes('/') || partialPath.includes('\\')) {
            return partialPath;
        }

        // Look for exact matches in recent files
        for (const file of context.recentFiles) {
            const fileName = file.split(/[/\\]/).pop();
            if (fileName === partialPath) {
                return file;
            }
        }

        // Look for partial matches
        for (const file of context.recentFiles) {
            if (file.includes(partialPath)) {
                return file;
            }
        }

        // Return the last active file if no matches found and partialPath is generic
        if (context.lastActiveFile && (partialPath === 'file' || partialPath === 'it' || partialPath === 'that')) {
            return context.lastActiveFile;
        }

        return undefined;
    }
}