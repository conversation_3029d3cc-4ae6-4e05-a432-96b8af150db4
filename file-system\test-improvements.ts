#!/usr/bin/env node

import { agent<PERSON><PERSON>, Thread } from './src/agent';
import { sessionManager } from './src/session-manager';

async function testFileSystemImprovements() {
    console.log('🧪 Testing File System Improvements\n');

    // Test 1: Create a session and add content to temp.md
    console.log('Test 1: Adding memory method to temp.md');
    const sessionId = sessionManager.createSession();
    
    let thread = new Thread([{
        type: "user_input",
        data: "add a new line '## memory_save_method' to temp.md"
    }]);

    let result = await agent<PERSON>oop(thread);
    console.log('✅ Test 1 completed\n');

    // Test 2: Replace without specifying full path (should use context)
    console.log('Test 2: Replacing text using context (no full path)');
    thread = sessionManager.getThread() || new Thread([]);
    thread.events.push({
        type: "user_input",
        data: "replace 'memory_save_method' with 'memory_save_function' in that file"
    });

    result = await agent<PERSON><PERSON>(thread);
    console.log('✅ Test 2 completed\n');

    // Test 3: Show conversation context
    console.log('Test 3: Conversation context');
    const context = sessionManager.getConversationSummary();
    console.log('Context:', context);
    console.log('✅ Test 3 completed\n');

    // Test 4: Show recent files
    console.log('Test 4: Recent files');
    const recentFiles = sessionManager.getRecentFiles();
    console.log('Recent files:', recentFiles);
    console.log('✅ Test 4 completed\n');

    // Test 5: Show file operation history
    console.log('Test 5: File operation history');
    const history = sessionManager.getFileOperationHistory();
    console.log('Operations:');
    history.forEach((op, index) => {
        console.log(`  ${index + 1}. ${op.success ? '✅' : '❌'} ${op.type} on ${op.filePath}`);
    });
    console.log('✅ Test 5 completed\n');

    console.log('🎉 All tests completed successfully!');
}

// Run tests
testFileSystemImprovements().catch(console.error);
