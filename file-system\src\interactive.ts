#!/usr/bin/env node

import * as readline from 'readline';
import { agent<PERSON>oop, Thread } from './agent';

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log('🤖 File System Agent - Interactive Mode');
console.log('Type your requests and press Enter. Type "exit" to quit.');
console.log('Examples:');
console.log('  - "read the file hello.txt"');
console.log('  - "create a file called test.txt with content \'Hello World!\'"');
console.log('  - "calculate 5 + 3 and save to result.txt"');
console.log('  - "replace \'Hello\' with \'Hi\' in hello.txt"');
console.log('---');

async function handleUserInput(input: string): Promise<void> {
    if (input.toLowerCase().trim() === 'exit') {
        console.log('👋 Goodbye!');
        rl.close();
        return;
    }

    if (input.trim() === '') {
        promptUser();
        return;
    }

    try {
        console.log('\n🔄 Processing your request...\n');
        
        // Create a new thread with the user input
        const thread = new Thread([{
            type: "user_input",
            data: input
        }]);

        // Run the agent loop
        const result = await agentLoop(thread);
        
        // Get the last event (should be the response)
        const lastEvent = result.events[result.events.length - 1];
        
        if (lastEvent && lastEvent.data) {
            if (lastEvent.data.intent === 'done_for_now') {
                console.log('✅ ' + lastEvent.data.message);
            } else if (lastEvent.data.intent === 'request_more_information') {
                console.log('❓ ' + lastEvent.data.message);
            } else {
                console.log('🔧 Tool executed:', lastEvent.data);
            }
        }
        
    } catch (error) {
        console.error('❌ Error:', error instanceof Error ? error.message : String(error));
    }
    
    console.log('\n---');
    promptUser();
}

function promptUser(): void {
    rl.question('💬 Your request: ', handleUserInput);
}

// Handle Ctrl+C gracefully
rl.on('SIGINT', () => {
    console.log('\n👋 Goodbye!');
    process.exit(0);
});

// Start the interactive session
promptUser();
