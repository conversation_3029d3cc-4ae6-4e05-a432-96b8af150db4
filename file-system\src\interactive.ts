#!/usr/bin/env node

import * as readline from 'readline';
import { agent<PERSON><PERSON>, Thread } from './agent';
import { sessionManager } from './session-manager';

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Initialize session
const sessionId = sessionManager.createSession();

console.log('🤖 File System Agent - Interactive Mode');
console.log(`📝 Session ID: ${sessionId}`);
console.log('Type your requests and press Enter. Type "exit" to quit.');
console.log('Special commands:');
console.log('  - "history" - Show recent file operations');
console.log('  - "files" - Show recent files');
console.log('  - "context" - Show conversation context');
console.log('Examples:');
console.log('  - "read the file hello.txt"');
console.log('  - "create a file called test.txt with content \'Hello World!\'"');
console.log('  - "replace \'Hello\' with \'Hi\' in that file" (uses context)');
console.log('  - "modify memory_save to memory_save" (smart path resolution)');
console.log('---');

async function handleUserInput(input: string): Promise<void> {
    if (input.toLowerCase().trim() === 'exit') {
        console.log('👋 Goodbye!');
        rl.close();
        return;
    }

    if (input.trim() === '') {
        promptUser();
        return;
    }

    // Handle special commands
    const command = input.toLowerCase().trim();
    if (command === 'history') {
        const operations = sessionManager.getFileOperationHistory();
        console.log('\n📋 Recent file operations:');
        if (operations.length === 0) {
            console.log('  No operations yet');
        } else {
            operations.slice(-10).forEach((op, index) => {
                const status = op.success ? '✅' : '❌';
                console.log(`  ${index + 1}. ${status} ${op.type} on ${op.filePath} (${op.timestamp.toLocaleTimeString()})`);
            });
        }
        console.log('\n---');
        promptUser();
        return;
    }

    if (command === 'files') {
        const files = sessionManager.getRecentFiles();
        console.log('\n📁 Recent files:');
        if (files.length === 0) {
            console.log('  No files accessed yet');
        } else {
            files.forEach((file, index) => {
                console.log(`  ${index + 1}. ${file}`);
            });
        }
        console.log('\n---');
        promptUser();
        return;
    }

    if (command === 'context') {
        const summary = sessionManager.getConversationSummary();
        console.log('\n🧠 Conversation context:');
        if (summary) {
            console.log(summary);
        } else {
            console.log('  No context available yet');
        }
        console.log('\n---');
        promptUser();
        return;
    }

    try {
        console.log('\n🔄 Processing your request...\n');

        // Get existing thread or create new one
        let thread = sessionManager.getThread();
        if (!thread) {
            thread = new Thread([]);
            sessionManager.updateThread(thread);
        }

        // Add user input to the existing thread
        thread.events.push({
            type: "user_input",
            data: input
        });

        // Run the agent loop
        const result = await agentLoop(thread);

        // Get the last event (should be the response)
        const lastEvent = result.events[result.events.length - 1];

        if (lastEvent && lastEvent.data) {
            if (lastEvent.data.intent === 'done_for_now') {
                console.log('✅ ' + lastEvent.data.message);
            } else if (lastEvent.data.intent === 'request_more_information') {
                console.log('❓ ' + lastEvent.data.message);
            } else {
                console.log('🔧 Tool executed:', lastEvent.data);
            }
        }

    } catch (error) {
        console.error('❌ Error:', error instanceof Error ? error.message : String(error));
    }

    console.log('\n---');
    promptUser();
}

function promptUser(): void {
    rl.question('💬 Your request: ', handleUserInput);
}

// Handle Ctrl+C gracefully
rl.on('SIGINT', () => {
    console.log('\n👋 Goodbye!');
    process.exit(0);
});

// Start the interactive session
promptUser();
