import * as fs from 'fs';
import * as path from 'path';
import { ReadFileTool as BAMLReadFileTool, WriteFileTool as BAMLWriteFileTool, ReplaceTool as BAMLReplaceTool } from '../baml_client/types';

export type FileSystemTool = BAMLReadFileTool | BAMLWriteFileTool | BAMLReplaceTool;

/**
 * Validates that a path is within the root directory for security
 */
function validatePath(filePath: string): string {
    // If path is not absolute, resolve it relative to ROOT_DIRECTORY
    const absolutePath = path.isAbsolute(filePath) ?
        path.resolve(filePath) :
        path.resolve(process.cwd(), filePath);

    const rootPath = path.resolve(process.cwd());

    if (!absolutePath.startsWith(rootPath)) {
        throw new Error(`Path ${filePath} is outside the allowed root directory ${rootPath}`);
    }

    return absolutePath;
}

/**
 * Checks if a file is a binary file by examining its content
 */
function isBinaryFile(filePath: string): boolean {
    try {
        const buffer = fs.readFileSync(filePath);
        const chunk = buffer.subarray(0, 512);

        // Check for null bytes which indicate binary content
        for (let i = 0; i < chunk.length; i++) {
            if (chunk[i] === 0) {
                return true;
            }
        }

        return false;
    } catch (error) {
        return false;
    }
}

/**
 * Gets the MIME type based on file extension
 */
function getMimeType(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes: { [key: string]: string } = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml',
        '.bmp': 'image/bmp',
        '.pdf': 'application/pdf'
    };
    
    return mimeTypes[ext] || 'application/octet-stream';
}

/**
 * Checks if a file is an image or PDF that should be returned as base64
 */
function isMediaFile(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.bmp', '.pdf'].includes(ext);
}

/**
 * Reads a file and returns its content
 */
export async function readFile(tool: BAMLReadFileTool): Promise<string> {
    try {
        const absolutePath = validatePath(tool.path);

        if (!fs.existsSync(absolutePath)) {
            return `Error: File not found: ${tool.path}`;
        }

        if (!fs.statSync(absolutePath).isFile()) {
            return `Error: Path is not a file: ${tool.path}`;
        }

        // Handle media files (images, PDFs)
        if (isMediaFile(absolutePath)) {
            const buffer = fs.readFileSync(absolutePath);
            const base64Data = buffer.toString('base64');
            const mimeType = getMimeType(absolutePath);

            return JSON.stringify({
                inlineData: {
                    mimeType: mimeType,
                    data: base64Data
                }
            });
        }

        // Check if it's a binary file
        if (isBinaryFile(absolutePath)) {
            return `Cannot display content of binary file: ${tool.path}`;
        }

        // Read text file
        const content = fs.readFileSync(absolutePath, 'utf-8');
        const lines = content.split('\n');

        // Handle offset and limit for text files
        if (tool.offset !== undefined || tool.limit !== undefined) {
            const offset = tool.offset || 0;
            const limit = tool.limit || 2000;

            if (offset >= lines.length) {
                return `Error: Offset ${offset} is beyond file length (${lines.length} lines)`;
            }

            const endLine = Math.min(offset + limit, lines.length);
            const selectedLines = lines.slice(offset, endLine);

            let result = selectedLines.join('\n');

            if (offset > 0 || endLine < lines.length) {
                const prefix = `[File content truncated: showing lines ${offset + 1}-${endLine} of ${lines.length} total lines...]\n`;
                result = prefix + result;
            }

            return result;
        }

        // Return full content, but truncate if too long
        const maxLines = 2000;
        if (lines.length > maxLines) {
            const truncatedLines = lines.slice(0, maxLines);
            const prefix = `[File content truncated: showing first ${maxLines} of ${lines.length} total lines...]\n`;
            return prefix + truncatedLines.join('\n');
        }

        return content;

    } catch (error) {
        return `Error reading file: ${error instanceof Error ? error.message : String(error)}`;
    }
}


/**
 * Writes content to a file
 */
export async function writeFile(tool: BAMLWriteFileTool): Promise<string> {
    try {
        const absolutePath = validatePath(tool.file_path);
        const fileExists = fs.existsSync(absolutePath);

        // Create parent directories if they don't exist
        const parentDir = path.dirname(absolutePath);
        if (!fs.existsSync(parentDir)) {
            fs.mkdirSync(parentDir, { recursive: true });
        }

        // Write the file
        fs.writeFileSync(absolutePath, tool.content, 'utf-8');

        if (fileExists) {
            return `Successfully overwrote file: ${tool.file_path}`;
        } else {
            return `Successfully created and wrote to new file: ${tool.file_path}`;
        }

    } catch (error) {
        return `Error writing file: ${error instanceof Error ? error.message : String(error)}`;
    }
}

/**
 * Replaces text within a file
 */
export async function replaceInFile(tool: BAMLReplaceTool): Promise<string> {
    try {
        const absolutePath = validatePath(tool.file_path);

        // Handle creating new file if old_string is empty
        if (tool.old_string === '') {
            if (fs.existsSync(absolutePath)) {
                return `Error: Cannot create new file, file already exists: ${tool.file_path}`;
            }

            const parentDir = path.dirname(absolutePath);
            if (!fs.existsSync(parentDir)) {
                fs.mkdirSync(parentDir, { recursive: true });
            }

            fs.writeFileSync(absolutePath, tool.new_string, 'utf-8');
            return `Created new file: ${tool.file_path} with provided content.`;
        }

        // Read existing file
        if (!fs.existsSync(absolutePath)) {
            return `Error: File not found: ${tool.file_path}`;
        }

        if (!fs.statSync(absolutePath).isFile()) {
            return `Error: Path is not a file: ${tool.file_path}`;
        }

        const content = fs.readFileSync(absolutePath, 'utf-8');

        // Count occurrences of old_string
        const occurrences = (content.match(new RegExp(escapeRegExp(tool.old_string), 'g')) || []).length;
        const expectedReplacements = tool.expected_replacements || 1;

        if (occurrences === 0) {
            return `Failed to edit, 0 occurrences found of the specified text in ${tool.file_path}`;
        }

        if (occurrences !== expectedReplacements) {
            return `Failed to edit, expected ${expectedReplacements} occurrences but found ${occurrences} in ${tool.file_path}`;
        }

        // Perform replacement
        const newContent = content.replace(new RegExp(escapeRegExp(tool.old_string), 'g'), tool.new_string);

        // Write back to file
        fs.writeFileSync(absolutePath, newContent, 'utf-8');

        return `Successfully modified file: ${tool.file_path} (${occurrences} replacements).`;

    } catch (error) {
        return `Error replacing in file: ${error instanceof Error ? error.message : String(error)}`;
    }
}

/**
 * Escapes special regex characters in a string
 */
function escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
