# File System Tools Implementation

本项目在现有的file-system框架基础上实现了三个核心文件系统工具：`read_file`、`write_file`和`replace`。这些工具遵循file-system.md规范，并与现有的计算器工具和人机交互功能完全集成。

## 实现的工具

### 1. `read_file` (ReadFile)
读取指定文件的内容，支持文本文件、图像文件和PDF文件。

**功能特性：**
- 支持文本文件的分页读取（offset和limit参数）
- 自动检测并处理二进制文件
- 图像和PDF文件返回base64编码格式
- 安全的路径验证，限制在根目录内

**使用示例：**
```bash
npx tsx src/index.ts "read the file test.txt"
npx tsx src/index.ts "read lines 10-20 from config.json"
```

### 2. `write_file` (WriteFile)
创建新文件或覆盖现有文件的内容。

**功能特性：**
- 自动创建父目录（如果不存在）
- 支持相对路径和绝对路径
- 安全的路径验证
- 明确的成功/失败反馈

**使用示例：**
```bash
npx tsx src/index.ts "create a file called hello.txt with content 'Hello World!'"
npx tsx src/index.ts "write the calculation result to output.txt"
```

### 3. `replace` (Edit)
在文件中替换指定的文本内容。

**功能特性：**
- 精确的文本匹配和替换
- 支持指定替换次数
- 如果old_string为空，则创建新文件
- 详细的错误报告（未找到、多次匹配等）

**使用示例：**
```bash
npx tsx src/index.ts "replace 'Hello World!' with 'Hello BAML!' in hello.txt"
npx tsx src/index.ts "update the version number in package.json"
```

## 技术实现

### 文件结构
```
file-system/
├── baml_src/
│   ├── agent.baml          # 主要的智能体逻辑和测试
│   ├── file_tools.baml     # 文件系统工具的BAML定义
│   ├── tool_calculator.baml # 计算器工具定义
│   └── clients.baml        # LLM客户端配置
├── src/
│   ├── agent.ts           # 智能体循环和工具处理
│   ├── file-tools.ts      # 文件系统工具的TypeScript实现
│   └── ...
└── baml_client/           # 自动生成的BAML客户端代码
```

### 安全特性
- **路径验证**: 所有文件操作都限制在项目根目录内
- **二进制文件检测**: 自动识别并安全处理二进制文件
- **错误处理**: 全面的错误捕获和用户友好的错误消息
- **类型安全**: 使用TypeScript和BAML的强类型系统

### 集成特性
- **多工具协作**: 文件系统工具可以与计算器工具和人机交互功能无缝配合
- **智能体循环**: 支持复杂的多步骤操作
- **结构化输出**: 所有工具都返回结构化的JSON响应

## 测试验证

项目包含全面的测试套件：

```bash
# 运行所有BAML测试
npx baml-cli test

# 测试基本功能
npx tsx src/index.ts "read the file test.txt"
npx tsx src/index.ts "create a file called demo.txt with content 'Demo content'"
npx tsx src/index.ts "replace 'Demo' with 'Example' in demo.txt"

# 测试复杂操作
npx tsx src/index.ts "calculate 5 + 3 and write the result to math_result.txt"
```

## 配置

### 模型配置
项目使用qwen2.5-32b-instruct-int4模型，通过OpenAI兼容的API接口：
- **端点**: https://gateway.chat.sensedeal.vip/v1
- **模型**: qwen2.5-32b-instruct-int4
- **认证**: Bearer token (已内置配置)

### 工具类型定义
```typescript
type FileSystemTools = ReadFileTool | WriteFileTool | ReplaceTool
type AllTools = CalculatorTools | FileSystemTools | HumanTools
```

## 使用场景

1. **文档处理**: 读取、编辑和创建各种文档文件
2. **配置管理**: 更新配置文件中的特定值
3. **数据处理**: 计算结果并保存到文件
4. **代码生成**: 创建和修改代码文件
5. **批量操作**: 结合计算器功能进行复杂的文件操作

## 扩展性

框架设计支持轻松添加新的工具类型：
1. 在`baml_src/`中定义新的工具类型
2. 在`src/`中实现对应的TypeScript逻辑
3. 在`agent.ts`中添加工具处理逻辑
4. 重新生成BAML客户端

这种模块化设计确保了系统的可维护性和可扩展性。
