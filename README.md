# 第0章 - Hello World

让我们从基本的TypeScript设置和hello world程序开始。

本指南使用TypeScript编写（是的，Python版本即将推出）

在工作坊步骤中每次文件编辑之间都有许多检查点，
所以即使您对TypeScript不是很熟悉，
您也应该能够跟上并运行每个示例。

要运行本指南，您需要安装相对较新版本的nodejs和npm

您可以使用任何您想要的nodejs版本管理器，[homebrew](https://formulae.brew.sh/formula/node)就可以

    brew install node@20

您应该看到node版本

    node --version

复制初始package.json

    cp ./walkthrough/00-package.json package.json

安装依赖

    npm install

复制tsconfig.json

    cp ./walkthrough/00-tsconfig.json tsconfig.json

添加.gitignore

    cp ./walkthrough/00-.gitignore .gitignore

创建src文件夹

    mkdir -p src

添加一个简单的hello world index.ts

    cp ./walkthrough/00-index.ts src/index.ts

运行它来验证

    npx tsx src/index.ts

您应该看到：

    hello, world!


# 第1章 - CLI和智能体循环

现在让我们添加BAML并创建我们的第一个带有CLI界面的智能体。

首先，我们需要安装[BAML](https://github.com/boundaryml/baml)
这是一个用于提示和结构化输出的工具。

    npm install @boundaryml/baml

初始化BAML

    npx baml-cli init

删除默认的resume.baml

    rm baml_src/resume.baml

添加我们的起始智能体，一个我们将构建的单一baml提示

    cp ./walkthrough/01-agent.baml baml_src/agent.baml

生成BAML客户端代码

    npx baml-cli generate

为本节启用BAML日志记录

    export BAML_LOG=debug

添加CLI界面

    cp ./walkthrough/01-cli.ts src/cli.ts

更新index.ts以使用CLI

    cp ./walkthrough/01-index.ts src/index.ts

添加智能体实现

    cp ./walkthrough/01-agent.ts src/agent.ts

BAML代码已配置为使用qwen2.5-32b-instruct-int4模型

项目已配置为使用兼容OpenAI格式的qwen2.5-32b-instruct-int4模型，通过gateway.chat.sensedeal.vip端点访问。

```rust
  function DetermineNextStep(thread: string) -> DoneForNow {
      client Qwen3  // 使用qwen2.5-32b-instruct-int4模型
      // ...
```

当前配置：
- **模型**: qwen2.5-32b-instruct-int4
- **API端点**: https://gateway.chat.sensedeal.vip/v1
- **认证**: Bearer token (已内置配置)

如果您想尝试更换模型，可以更改`client`行或在`baml_src/clients.baml`中添加新的客户端配置。

[baml客户端的文档可以在这里找到](https://docs.boundaryml.com/guide/baml-basics/switching-llms)

例如，您可以配置[gemini](https://docs.boundaryml.com/ref/llm-client-providers/google-ai-gemini)
或[anthropic](https://docs.boundaryml.com/ref/llm-client-providers/anthropic)作为您的模型提供商。

例如，要使用带有OPENAI_API_KEY的openai，您可以这样做：

    client "openai/gpt-4o"


无需设置环境变量，模型配置已内置

试试看

    npx tsx src/index.ts hello

您应该看到来自模型的熟悉响应

    {
      intent: 'done_for_now',
      message: 'Hello! How can I assist you today?'
    }


# 第2章 - 添加计算器工具

让我们为我们的智能体添加一些计算器工具。

让我们首先为计算器添加工具定义

这些是简单的结构化输出，我们将要求模型
在智能体循环中作为"下一步"返回。

    cp ./walkthrough/02-tool_calculator.baml baml_src/tool_calculator.baml

现在，让我们更新智能体的DetermineNextStep方法以
将计算器工具作为潜在的下一步公开

    cp ./walkthrough/02-agent.baml baml_src/agent.baml

生成更新的BAML客户端

    npx baml-cli generate

试试计算器

    npx tsx src/index.ts 'can you add 3 and 4'

您应该看到对计算器的工具调用

    {
      intent: 'add',
      a: 3,
      b: 4
    }


# 第3章 - 在循环中处理工具调用

现在让我们添加一个真正的智能体循环，可以运行工具并从LLM获得最终答案。

首先，让我们更新智能体来处理工具调用

    cp ./walkthrough/03-agent.ts src/agent.ts

现在，让我们试试看

    npx tsx src/index.ts 'can you add 3 and 4'

您应该看到智能体调用工具然后返回结果

    {
      intent: 'done_for_now',
      message: 'The sum of 3 and 4 is 7.'
    }

对于下一步，我们将进行更复杂的计算，让我们关闭baml日志以获得更简洁的输出

    export BAML_LOG=off

尝试多步计算

    npx tsx src/index.ts 'can you add 3 and 4, then add 6 to that result'

您会注意到像乘法和除法这样的工具不可用

    npx tsx src/index.ts 'can you multiply 3 and 4'

接下来，让我们为其余的计算器工具添加处理程序

    cp ./walkthrough/03b-agent.ts src/agent.ts

测试减法

    npx tsx src/index.ts 'can you subtract 3 from 4'

现在，让我们测试乘法工具

    npx tsx src/index.ts 'can you multiply 3 and 4'

最后，让我们测试具有多个操作的更复杂计算

    npx tsx src/index.ts 'can you multiply 3 and 4, then divide the result by 2 and then add 12 to that result'

恭喜，您已经迈出了手工制作智能体循环的第一步。

从这里开始，我们将开始融入一些更中级和高级的
12-factor智能体概念。



# 第4章 - 为agent.baml添加测试

让我们为我们的BAML智能体添加一些测试。

首先，保持baml日志启用

    export BAML_LOG=debug

接下来，让我们为智能体添加一些测试

我们将从一个简单的测试开始，检查智能体处理
基本计算的能力。

    cp ./walkthrough/04-agent.baml baml_src/agent.baml

运行测试

    npx baml-cli test

现在，让我们用断言来改进测试！

断言是确保智能体按预期工作的好方法，
并且可以轻松扩展以检查更复杂的行为。

    cp ./walkthrough/04b-agent.baml baml_src/agent.baml

运行测试

    npx baml-cli test

当您添加更多测试时，您可以禁用日志以保持输出清洁。
您可能希望在迭代特定测试时打开它们。

    export BAML_LOG=off

现在，让我们添加一些更复杂的测试用例，
我们从正在进行的智能体上下文窗口的中间恢复

    cp ./walkthrough/04c-agent.baml baml_src/agent.baml

让我们尝试运行它

    npx baml-cli test


# 第5章 - 多个人类工具

在本节中，我们将添加对多个用于
联系人类的工具的支持。

对于本节，我们将禁用baml日志。如果您想查看更多详细信息，可以选择启用它们。

    export BAML_LOG=off

首先，让我们添加一个可以从人类请求澄清的工具

这将与"done_for_now"工具不同，
并且可以用于更灵活地处理智能体中不同类型的人类交互。

    cp ./walkthrough/05-agent.baml baml_src/agent.baml

接下来，让我们重新生成客户端代码

注意 - 如果您正在使用BAML的VSCode扩展，
当您在编辑器中保存文件时，客户端将自动重新生成。

    npx baml-cli generate

现在，让我们更新智能体以使用新工具

    cp ./walkthrough/05-agent.ts src/agent.ts

接下来，让我们更新CLI以通过在CLI上请求用户输入
来处理澄清请求

    cp ./walkthrough/05-cli.ts src/cli.ts

让我们试试看

    npx tsx src/index.ts 'can you multiply 3 and FD*(#F&& '

接下来，让我们添加一个测试来检查智能体处理
澄清请求的能力

    cp ./walkthrough/05b-agent.baml baml_src/agent.baml

现在我们可以再次运行测试

    npx baml-cli test

您会注意到新测试通过了，但hello world测试失败了

这是因为智能体的默认行为是返回"done_for_now"

    cp ./walkthrough/05c-agent.baml baml_src/agent.baml

验证测试通过

    npx baml-cli test


# 第6章 - 使用推理自定义您的提示

在本节中，我们将探索如何使用推理步骤
自定义智能体的提示。

这是[因子2 - 拥有您的提示](https://github.com/humanlayer/12-factor-agents/blob/main/content/factor-2-own-your-prompts.md)的核心

在AI That Works上有关于推理的深入探讨[推理模型与推理步骤](https://github.com/hellovai/ai-that-works/tree/main/2025-04-07-reasoning-models-vs-prompts)

对于本节，启用baml日志会很有帮助

    export BAML_LOG=debug

更新智能体提示以包含推理步骤

    cp ./walkthrough/06-agent.baml baml_src/agent.baml

生成更新的客户端

    npx baml-cli generate

现在，您可以用简单的提示试试看

    npx tsx src/index.ts 'can you multiply 3 and 4'

您应该从baml日志中看到显示推理步骤的输出

#### 可选挑战

在您的工具输出格式中添加一个包含推理步骤的字段！



# 第7章 - 自定义您的上下文窗口

在本节中，我们将探索如何自定义智能体的上下文窗口。

这是[因子3 - 拥有您的上下文窗口](https://github.com/humanlayer/12-factor-agents/blob/main/content/factor-3-own-your-context-window.md)的核心

更新智能体以为模型美化打印上下文窗口

    cp ./walkthrough/07-agent.ts src/agent.ts

测试格式化

    BAML_LOG=info npx tsx src/index.ts 'can you multiply 3 and 4, then divide the result by 2 and then add 12 to that result'

接下来，让我们更新智能体以使用XML格式

这是向模型传递数据的非常流行的格式，

除其他外，因为XML的令牌效率。

    cp ./walkthrough/07b-agent.ts src/agent.ts

让我们试试看

    BAML_LOG=info npx tsx src/index.ts 'can you multiply 3 and 4, then divide the result by 2 and then add 12 to that result'

让我们更新我们的测试以匹配新的输出格式

    cp ./walkthrough/07c-agent.baml baml_src/agent.baml

查看更新的测试

    npx baml-cli test


# 第8章 - 添加API端点

添加Express服务器以通过HTTP公开智能体。

对于本节，我们将禁用baml日志。如果您想查看更多详细信息，可以选择启用它们。

    export BAML_LOG=off

安装Express和类型

    npm install express && npm install --save-dev @types/express supertest

添加服务器实现

    cp ./walkthrough/08-server.ts src/server.ts

启动服务器

    npx tsx src/server.ts

用curl测试（在另一个终端中）

    curl -X POST http://localhost:3000/thread \
  -H "Content-Type: application/json" \
  -d '{"message":"can you add 3 and 4"}'

您应该从智能体获得答案，其中包括
智能体跟踪，以类似这样的消息结尾：

    {"intent":"done_for_now","message":"The sum of 3 and 4 is 7."}


# 第9章 - 内存状态和异步澄清

添加状态管理和异步澄清支持。

对于本节，我们将禁用baml日志。如果您想查看更多详细信息，可以选择启用它们。

    export BAML_LOG=off

为线程添加一些简单的内存状态管理

    cp ./walkthrough/09-state.ts src/state.ts

更新服务器以使用状态管理

* 使用`ThreadStore`添加线程状态管理
* 从/thread端点返回线程ID和响应URL
* 实现GET /thread/:id
* 实现POST /thread/:id/response

    cp ./walkthrough/09-server.ts src/server.ts

启动服务器

    npx tsx src/server.ts

测试澄清流程

    curl -X POST http://localhost:3000/thread \
  -H "Content-Type: application/json" \
  -d '{"message":"can you multiply 3 and xyz"}'


# 第10章 - 添加人类批准

添加对操作的人类批准支持。

对于本节，我们将禁用baml日志。如果您想查看更多详细信息，可以选择启用它们。

    export BAML_LOG=off

更新服务器以处理人类批准

* 导入`handleNextStep`以执行批准的操作
* 添加两种负载类型以区分批准和响应
* 在端点中以不同方式处理响应和批准
* 当出现问题时显示更好的错误消息

    cp ./walkthrough/10-server.ts src/server.ts

为智能体添加一些方法来处理批准和响应

    cp ./walkthrough/10-agent.ts src/agent.ts

启动服务器

    npx tsx src/server.ts

测试带批准的除法

    curl -X POST http://localhost:3000/thread \
  -H "Content-Type: application/json" \
  -d '{"message":"can you divide 3 by 4"}'

您应该看到：

    {
      "thread_id": "2b243b66-215a-4f37-8bc6-9ace3849043b",
      "events": [
        {
          "type": "user_input",
          "data": "can you divide 3 by 4"
        },
        {
          "type": "tool_call",
          "data": {
            "intent": "divide",
            "a": 3,
            "b": 4,
            "response_url": "/thread/2b243b66-215a-4f37-8bc6-9ace3849043b/response"
          }
        }
      ]
    }

用另一个curl调用拒绝请求，更改线程ID

    curl -X POST 'http://localhost:3000/thread/{thread_id}/response' \
  -H "Content-Type: application/json" \
  -d '{"type": "approval", "approved": false, "comment": "I dont think thats right, use 5 instead of 4"}'

您应该看到：最后的工具调用现在是`"intent":"divide","a":3,"b":5`

现在您可以批准操作

    curl -X POST 'http://localhost:3000/thread/{thread_id}/response' \
  -H "Content-Type: application/json" \
  -d '{"type": "approval", "approved": true}'

您应该看到最终消息包含工具响应和最终结果！


# 第11章 - 通过电子邮件进行人类批准

在本节中，我们将添加通过电子邮件进行人类批准的支持。

这开始时会有点做作，只是为了掌握概念 -

我们将从CLI调用工作流开始，但对`divide`
和`request_more_information`的批准将通过电子邮件处理，
然后最终的`done_for_now`答案将打印回CLI

虽然做作，但这是您从
[因子7 - 使用工具联系人类](https://github.com/humanlayer/12-factor-agents/blob/main/content/factor-7-contact-humans-with-tools.md)
获得的灵活性的绝佳示例

对于本节，我们将禁用baml日志。如果您想查看更多详细信息，可以选择启用它们。

    export BAML_LOG=off

安装HumanLayer

    npm install humanlayer

更新CLI以通过电子邮件将`divide`和`request_more_information`发送给人类

    cp ./walkthrough/11-cli.ts src/cli.ts

运行CLI

    npx tsx src/index.ts 'can you divide 4 by 5'

您程序的最后一行应该提到人类审查步骤

    nextStep { intent: 'divide', a: 4, b: 5 }
    HumanLayer: Requested human approval from HumanLayer cloud

继续回复电子邮件并提供一些反馈，然后您可以批准操作。

这就是全部 - 在下一章中，我们将构建一个完全由电子邮件驱动的
使用webhooks进行人类批准的工作流智能体


# 第XX章 - HumanLayer Webhook集成

前面的章节使用humanlayer SDK的"同步模式" - 这意味着
每次我们等待人类批准时，我们都会坐在循环中
轮询直到收到人类响应。

这显然不理想，特别是对于生产工作负载，
所以在本节中我们将实现[因子6 - 使用简单API启动/暂停/恢复](https://github.com/humanlayer/12-factor-agents/blob/main/content/factor-6-launch-pause-resume.md)
通过更新服务器在联系人类后结束处理，并使用webhooks接收结果。

在服务器中添加代码以初始化humanlayer

    cp ./walkthrough/12-1-server-init.ts src/server.ts

接下来，让我们更新/thread端点以

1. 异步处理请求，立即返回
2. 在request_more_information和done_for_now调用上创建人类联系

更新服务器以能够处理request_clarification响应

- 删除旧的/response端点和类型
- 更新/thread端点以异步运行处理，立即返回
- 在请求人类响应时发送state.threadId
- 添加handleHumanResponse函数来处理人类响应
- 添加/webhook端点来处理webhook响应

    cp ./walkthrough/12a-server.ts src/server.ts

在另一个终端中启动服务器

    npx tsx src/server.ts

现在服务器正在运行，向'/thread'端点发送负载


## 配置

智能体使用通过OpenAI兼容API的Qwen 2.5 32B模型：
- **模型**: qwen2.5-32b-instruct-int4
- **API端点**: https://gateway.chat.sensedeal.vip/v1
- **认证**: Bearer token

## 架构

本项目遵循12-factor智能体方法论：

1. **自然语言到工具调用**: 将用户请求转换为结构化工具调用
2. **拥有您的提示**: 基于BAML的提示管理
3. **拥有您的上下文窗口**: 基于XML的上下文序列化
4. **工具即结构化输出**: 文件操作作为结构化工具调用
5. **无状态处理**: 每个操作都是独立的

## 文件结构

```
file-system-agent/
├── baml_src/           # BAML配置
│   ├── agent.baml      # 智能体逻辑和测试
│   ├── clients.baml    # LLM客户端配置
│   ├── file_tools.baml # 工具定义
│   └── generators.baml # 代码生成配置
├── src/                # TypeScript源码
│   ├── agent.ts        # 核心智能体逻辑
│   ├── cli.ts          # 命令行界面
│   ├── file-tools.ts   # 文件系统操作
│   ├── index.ts        # 入口点
│   └── test.ts         # 测试套件
└── baml_client/        # 生成的BAML客户端（自动生成）
```

## 安全特性

- **路径验证**: 所有文件路径都针对根目录进行验证
- **二进制文件检测**: 防止将二进制文件作为文本读取
- **错误处理**: 对所有操作进行全面的错误处理
- **上下文要求**: 替换操作需要足够的上下文以确保安全
